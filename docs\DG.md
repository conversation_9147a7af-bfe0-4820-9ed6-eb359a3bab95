# Recoater HMI - Developer Guide

This document provides technical details for developers working on the Recoater HMI. It includes an overview of the architecture and a detailed API reference.

## 1. Architecture

The system uses a decoupled backend/frontend architecture.

-   **`backend` (FastAPI):** Acts as a proxy and business logic layer. It communicates directly with the recoater hardware's API. It exposes a simplified REST API and a WebSocket to the frontend. This isolates the frontend from the complexities of the hardware API and allows for state management and background tasks (like polling).

-   **`frontend` (Vue.js):** A pure Single-Page Application (SPA) that handles all user interaction. It gets all its data from the backend via API calls and receives real-time updates through a WebSocket connection. It never communicates with the recoater directly.

## 2. Key Components

### Backend Components

-   **`backend/services/recoater_client.py`:** Core service class that implements methods for every call to the recoater's hardware API. Handles connection management, error handling, and provides a clean interface for hardware communication.

-   **`backend/app/api/recoater.py`:** FastAPI router defining the status-related REST endpoints (`/api/v1/status/` and `/api/v1/status/health`). Handles error responses and provides structured status information.

-   **`backend/app/main.py`:** Main FastAPI application with WebSocket support for real-time updates. Includes background polling task, CORS configuration, and application lifecycle management.

-   **`backend/.env`:** Environment configuration file containing recoater API settings, WebSocket configuration, and debug options.

### Frontend Components

-   **`frontend/src/components/StatusIndicator.vue`:** Real-time status indicator component that displays connection status with colored dots and manages WebSocket connections.

-   **`frontend/src/views/StatusView.vue`:** Main status page showing detailed system information, connection status, and error details.

-   **`frontend/src/stores/status.js`:** Pinia store for centralized status management, WebSocket handling, and state synchronization.

-   **`frontend/src/services/api.js`:** Axios-based API service for HTTP communication with the backend, including request/response interceptors.

-   **`frontend/src/App.vue`:** Main application shell with navigation menu and header layout.

### Development and Testing

-   **`backend/tests/`:** Comprehensive test suite using pytest and pytest-mock for API endpoints and service classes.

-   **`frontend/tests/`:** Vitest-based test suite for Vue components, stores, and services.

## 3. Current Implementation Status (Phase 1 & 2)

### Completed Features

**Backend (FastAPI):**
- ✅ Basic FastAPI application structure
- ✅ RecoaterClient service for hardware communication
- ✅ Status API endpoints (`/api/v1/status/`, `/api/v1/status/health`)
- ✅ **Axis API endpoints (`/api/v1/axis/*`) - Phase 2**
- ✅ WebSocket support for real-time updates
- ✅ **WebSocket axis data integration - Phase 2**
- ✅ Background polling of recoater status
- ✅ Environment configuration management
- ✅ Comprehensive test coverage (33 tests passing)

**Frontend (Vue.js):**
- ✅ Vue 3 application with Vite build system
- ✅ Real-time status indicator component
- ✅ Status page with detailed system information
- ✅ **Complete Axis Control interface - Phase 2**
- ✅ Pinia store for state management
- ✅ **Enhanced store with axis data support - Phase 2**
- ✅ WebSocket integration for live updates
- ✅ Responsive design and navigation
- ✅ Test coverage with Vitest

**Integration:**
- ✅ End-to-end connectivity between frontend and backend
- ✅ Real-time status updates via WebSocket
- ✅ Error handling and connection management
- ✅ Development environment setup

### Phase 2 Architecture (Axis Control) - COMPLETED

**Backend Components:**

1. **Extended RecoaterClient** (`backend/services/recoater_client.py`)
   - Axis control methods following hardware API patterns
   - Consistent error handling and type safety
   - Methods: `get_axis_status()`, `move_axis()`, `home_axis()`, `set_gripper_state()`

2. **Axis API Router** (`backend/app/api/axis.py`)
   - RESTful endpoints for all axis operations
   - Pydantic models for request/response validation
   - Comprehensive error handling and status codes
   - Endpoints for X/Z axis movement, homing, and gripper control

3. **WebSocket Integration** (`backend/app/main.py`)
   - Real-time axis status polling every 1 second
   - Axis data included in WebSocket broadcasts
   - Graceful handling of unavailable axis endpoints

**Frontend Components:**

1. **AxisView Component** (`frontend/src/views/AxisView.vue`)
   - Complete axis control interface with real-time updates
   - Composition API with reactive state management
   - Input validation and error handling
   - Responsive design with accessibility features

2. **Enhanced Status Store** (`frontend/src/stores/status.js`)
   - Axis data state management
   - WebSocket message handling for axis updates
   - Computed properties for connection status

3. **Extended API Service** (`frontend/src/services/api.js`)
   - Axios-based methods for all axis endpoints
   - Consistent error handling and response processing

### Planned Features (Future Phases)

**Phase 3 - Drum Control:**
- Drum control interfaces (suction, ejection, rotation)
- Blade/screw positioning controls
- Drum configuration management

**Phase 4 - Print Management:**
- Geometry file upload (.CLI, .PNG)
- Print job configuration and execution
- Layer preview functionality

**Phase 5 - Configuration:**
- System configuration management
- Build space parameters
- Advanced settings

## 4. Development Workflow

### Running the Application

1. **Backend Development:**
   ```bash
   cd backend
   pip install -r requirements.txt
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Frontend Development:**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Running Tests:**
   ```bash
   # Backend tests
   cd backend && python -m pytest tests/ -v

   # Frontend tests
   cd frontend && npm test
   ```

### API Endpoints

**Status Endpoints:**
- `GET /api/v1/status/` - Get current system status
- `GET /api/v1/status/health` - Health check
- `WebSocket /ws` - Real-time status updates

## 5. Recoater Hardware API Reference

This reference is auto-generated from the provided `openapi.json` file. The backend service (`recoater_client.py`) should implement methods to call these endpoints.

### Recoater Endpoints

#### `GET /config`

- **Summary:** Get the recoater configuration variables.

- **Description:** Returns the configuration variables of the recoater.

#### `PUT /config`

- **Summary:** Set the recoater configuration variables.

- **Description:** Defines the configuration variables of the recoater.

- **Request Body (`application/json`):**

  - `build_space_diameter` (number, optional): The diameter of the build space [mm].

  - `build_space_dimensions` (object, optional):

    - `length` (number): The length of the build space [mm].

    - `width` (number): The width of the build space [mm].

  - `ejection_matrix_size` (integer, optional): The number of points in the ejection matrix.

  - `gaps` (array, optional): The list of gaps between the drums.

  - `resolution` (integer, optional): The resolution of the recoater, the size of one pixel [µm].

#### `GET /drums`

- **Summary:** Get drums info.

- **Description:** Returns information about the drums.

#### `GET /drums/{drum_id}`

- **Summary:** Get drum info.

- **Description:** Returns information about the specified drum.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

#### `PUT /drums/{drum_id}/config`

- **Summary:** Set drum config.

- **Description:** Defines the configuration of the specified drum.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `powder_offset` (integer, optional): The drum's powder offset [pixel].

  - `theta_offset` (number, required): The drum's theta offset [mm].

#### `PUT /drums/{drum_id}/ejection`

- **Summary:** Set drum ejection pressure.

- **Description:** Defines the target ejection pressure that the specified drum has to reach.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `target` (number, required): The target ejection pressure of the drum.

  - `unit` (string, optional): Units: pascal, bar.

#### `PUT /drums/{drum_id}/geometry`

- **Summary:** Set the drum geometry.

- **Description:** Defines the geometry of the specified drum. The geometry can either be a PNG file or a CLI file.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/octet-stream`):** Binary data.

#### `DELETE /drums/{drum_id}/geometry`

- **Summary:** Delete the drum geometry.

- **Description:** Removes the current geometry of the specified drum.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

#### `POST /drums/{drum_id}/motion`

- **Summary:** Post a motion command.

- **Description:** Creates a motion command if possible.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `mode` (string, required): Motion's mode: absolute, relative, turns, speed, homing.

  - `speed` (number, required): The speed of the motion [mm/s].

  - `distance` (number, optional): The absolute or relative distance of the motion [mm].

  - `turns` (number, optional): The number of turns of the motion.

#### `DELETE /drums/{drum_id}/motion`

- **Summary:** Delete the current motion command.

- **Description:** Cancels and removes the current motion command.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

#### `PUT /drums/{drum_id}/suction`

- **Summary:** Set drum suction pressure target.

- **Description:** Defines the target suction pressure that the specified drum has to reach.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `target` (number, required): The target suction pressure of the drum [Pa].

#### `POST /drums/{drum_id}/blade/screws/motion`

- **Summary:** Post a motion command.

- **Description:** Creates a motion command if possible.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `mode` (string, required): Motion's mode: absolute, relative, homing.

  - `distance` (number, optional): The absolute or relative distance of the motion [µm].

#### `POST /drums/{drum_id}/blade/screws/{screw_id}/motion`

- **Summary:** Post a motion command.

- **Description:** Creates a motion command if possible for a single screw.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

  - `screw_id` (integer, path, required): The drum blade screw's ID.

- **Request Body (`application/json`):**

  - `distance` (number, required): The relative distance of the motion [µm].

#### `GET /layer/preview`

- **Summary:** Get layer preview.

- **Description:** Returns a PNG image preview of the layer.

#### `PUT /layer/parameters`

- **Summary:** Set layer's parameters.

- **Description:** Defines the parameters of the current layer.

- **Request Body (`application/json`):**

  - `filling_id` (integer, required): The ID of the drum with the filling material powder.

  - `speed` (number, required): The patterning speed [mm/s].

  - `powder_saving` (boolean, optional, default: true): A flag indicating if the powder saving strategies are used.

  - `x_offset` (number, optional): The offset along the X axis [mm].

#### `PUT /leveler/pressure`

- **Summary:** Set leveler pressure target.

- **Description:** Defines the target pressure that the leveler has to reach.

- **Request Body (`application/json`):**

  - `target` (number, required): The target pressure of the leveler [Pa].

#### `POST /print/job`

- **Summary:** Post a print job.

- **Description:** Creates a printing job if the server is ready to start.

#### `DELETE /print/job`

- **Summary:** Delete the current print job.

- **Description:** Cancels and removes the current printing job.

#### `GET /state`

- **Summary:** Get the recoater's state.

- **Description:** Returns the current state of the recoater (ready, printing, error).

---

*This is a summarized list. For full details on all endpoints and their responses, refer to the `openapi.json` file.*
