"""
Tests for Axis API Router
=========================

This module contains tests for the axis control API endpoints.
All tests use mocked RecoaterClient to avoid hardware dependencies.
"""

import pytest
from unittest.mock import patch
from fastapi.testclient import TestClient
from app.main import app
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError

client = TestClient(app)

class TestAxisAPI:
    """Test class for axis API endpoints."""
    
    @patch('app.main.recoater_client')
    def test_get_axis_status_success(self, mock_client):
        """Test successful axis status retrieval."""
        # Arrange
        mock_client.get_axis_status.return_value = {
            "position": 12.5,
            "running": False,
            "homed": True
        }
        
        # Act
        response = client.get("/api/v1/axis/x")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["axis"] == "x"
        assert data["connected"] is True
        assert data["status"]["position"] == 12.5
        mock_client.get_axis_status.assert_called_once_with("x")
    
    @patch('app.main.recoater_client')
    def test_get_axis_status_connection_error(self, mock_client):
        """Test axis status retrieval with connection error."""
        # Arrange
        mock_client.get_axis_status.side_effect = RecoaterConnectionError("Connection failed")
        
        # Act
        response = client.get("/api/v1/axis/z")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["axis"] == "z"
        assert data["connected"] is False
        assert data["status"] is None
        assert "Failed to connect" in data["error"]
        mock_client.get_axis_status.assert_called_once_with("z")
    
    @patch('app.main.recoater_client')
    def test_get_axis_status_api_error(self, mock_client):
        """Test axis status retrieval with API error."""
        # Arrange
        mock_client.get_axis_status.side_effect = RecoaterAPIError("API error 404")
        
        # Act
        response = client.get("/api/v1/axis/x")
        
        # Assert
        assert response.status_code == 502
        data = response.json()
        assert "Recoater API error" in data["detail"]
        mock_client.get_axis_status.assert_called_once_with("x")
    
    @patch('app.main.recoater_client')
    def test_move_axis_success(self, mock_client):
        """Test successful axis movement."""
        # Arrange
        mock_client.move_axis.return_value = {"command_id": "motion_123"}
        motion_data = {
            "distance": 10.0,
            "speed": 5.0,
            "mode": "relative"
        }
        
        # Act
        response = client.post("/api/v1/axis/x/motion", json=motion_data)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["axis"] == "x"
        assert data["command"] == "motion"
        assert data["success"] is True
        assert data["parameters"]["distance"] == 10.0
        mock_client.move_axis.assert_called_once_with(
            axis="x", distance=10.0, speed=5.0, mode="relative"
        )
    
    @patch('app.main.recoater_client')
    def test_move_axis_connection_error(self, mock_client):
        """Test axis movement with connection error."""
        # Arrange
        mock_client.move_axis.side_effect = RecoaterConnectionError("Connection failed")
        motion_data = {
            "distance": 5.0,
            "speed": 2.0,
            "mode": "absolute"
        }
        
        # Act
        response = client.post("/api/v1/axis/z/motion", json=motion_data)
        
        # Assert
        assert response.status_code == 503
        data = response.json()
        assert "Failed to connect" in data["detail"]
        mock_client.move_axis.assert_called_once_with(
            axis="z", distance=5.0, speed=2.0, mode="absolute"
        )
    
    @patch('app.main.recoater_client')
    def test_home_axis_success(self, mock_client):
        """Test successful axis homing."""
        # Arrange
        mock_client.home_axis.return_value = {"command_id": "home_456"}
        homing_data = {"speed": 15.0}
        
        # Act
        response = client.post("/api/v1/axis/x/home", json=homing_data)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["axis"] == "x"
        assert data["command"] == "homing"
        assert data["success"] is True
        assert data["parameters"]["speed"] == 15.0
        mock_client.home_axis.assert_called_once_with(axis="x", speed=15.0)
    
    @patch('app.main.recoater_client')
    def test_home_axis_default_speed(self, mock_client):
        """Test axis homing with default speed."""
        # Arrange
        mock_client.home_axis.return_value = {"command_id": "home_789"}
        homing_data = {}  # No speed specified, should use default
        
        # Act
        response = client.post("/api/v1/axis/z/home", json=homing_data)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["axis"] == "z"
        assert data["parameters"]["speed"] == 10.0  # Default speed
        mock_client.home_axis.assert_called_once_with(axis="z", speed=10.0)
    
    @patch('app.main.recoater_client')
    def test_get_axis_motion_success(self, mock_client):
        """Test successful axis motion status retrieval."""
        # Arrange
        mock_client.get_axis_motion.return_value = {
            "mode": "relative",
            "distance": 5.0,
            "speed": 3.0,
            "progress": 0.5
        }
        
        # Act
        response = client.get("/api/v1/axis/x/motion")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["axis"] == "x"
        assert data["connected"] is True
        assert data["motion"]["mode"] == "relative"
        mock_client.get_axis_motion.assert_called_once_with("x")
    
    @patch('app.main.recoater_client')
    def test_cancel_axis_motion_success(self, mock_client):
        """Test successful axis motion cancellation."""
        # Arrange
        mock_client.cancel_axis_motion.return_value = {"cancelled": True}
        
        # Act
        response = client.delete("/api/v1/axis/z/motion")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["axis"] == "z"
        assert data["command"] == "cancel_motion"
        assert data["success"] is True
        mock_client.cancel_axis_motion.assert_called_once_with("z")
    
    @patch('app.main.recoater_client')
    def test_set_gripper_state_success(self, mock_client):
        """Test successful gripper state setting."""
        # Arrange
        mock_client.set_gripper_state.return_value = {"state": "enabled"}
        gripper_data = {"enabled": True}
        
        # Act
        response = client.put("/api/v1/axis/gripper/state", json=gripper_data)
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["command"] == "set_gripper_state"
        assert data["success"] is True
        assert data["parameters"]["enabled"] is True
        mock_client.set_gripper_state.assert_called_once_with(True)
    
    @patch('app.main.recoater_client')
    def test_get_gripper_state_success(self, mock_client):
        """Test successful gripper state retrieval."""
        # Arrange
        mock_client.get_gripper_state.return_value = {
            "enabled": False,
            "position": "open"
        }
        
        # Act
        response = client.get("/api/v1/axis/gripper/state")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["connected"] is True
        assert data["gripper_state"]["enabled"] is False
        mock_client.get_gripper_state.assert_called_once()
    
    @patch('app.main.recoater_client')
    def test_get_gripper_state_connection_error(self, mock_client):
        """Test gripper state retrieval with connection error."""
        # Arrange
        mock_client.get_gripper_state.side_effect = RecoaterConnectionError("Connection failed")
        
        # Act
        response = client.get("/api/v1/axis/gripper/state")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["connected"] is False
        assert data["gripper_state"] is None
        assert "Failed to connect" in data["error"]
        mock_client.get_gripper_state.assert_called_once()
    
    def test_invalid_axis_parameter(self):
        """Test API with invalid axis parameter."""
        # Act
        response = client.get("/api/v1/axis/invalid")
        
        # Assert
        assert response.status_code == 422  # Validation error
        data = response.json()
        assert "detail" in data
    
    def test_invalid_motion_data(self):
        """Test motion endpoint with invalid data."""
        # Arrange
        invalid_data = {
            "distance": "not_a_number",
            "speed": -5.0,  # Negative speed should be invalid
            "mode": "invalid_mode"
        }
        
        # Act
        response = client.post("/api/v1/axis/x/motion", json=invalid_data)
        
        # Assert
        assert response.status_code == 422  # Validation error
        data = response.json()
        assert "detail" in data
