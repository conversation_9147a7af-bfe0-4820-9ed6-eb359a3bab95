/**
 * Tests for AxisView Component
 * ============================
 * 
 * This module contains tests for the AxisView component.
 * All tests use mocked API services to avoid backend dependencies.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import AxisView from '@/views/AxisView.vue'
import { useStatusStore } from '@/stores/status'
import apiService from '@/services/api'

// Mock the API service
vi.mock('@/services/api', () => ({
  default: {
    moveAxis: vi.fn(),
    homeAxis: vi.fn(),
    setGripperState: vi.fn(),
    getAxisStatus: vi.fn(),
    getGripperState: vi.fn()
  }
}))

describe('AxisView', () => {
  let wrapper
  let statusStore

  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia())

    // Reset all mocks
    vi.clearAllMocks()
  })

  const createWrapper = (storeState = {}) => {
    const pinia = createPinia()
    setActivePinia(pinia)

    // Set up store with default or provided state
    const store = useStatusStore()
    store.isConnected = storeState.isConnected ?? true
    store.axisData = storeState.axisData ?? {
      x: { position: 10.5, running: false },
      z: { position: 5.2, running: false },
      gripper: { enabled: false }
    }

    return mount(AxisView, {
      global: {
        plugins: [pinia]
      }
    })
  }

  it('renders correctly', () => {
    wrapper = createWrapper()
    
    expect(wrapper.find('.axis-view').exists()).toBe(true)
    expect(wrapper.find('.view-title').text()).toBe('Axis Control')
    expect(wrapper.find('.status-card').exists()).toBe(true)
    expect(wrapper.find('.axis-controls').exists()).toBe(true)
    expect(wrapper.find('.gripper-control').exists()).toBe(true)
  })

  it('displays connection status correctly when connected', () => {
    wrapper = createWrapper({ isConnected: true })

    const statusDot = wrapper.find('.status-dot')
    expect(statusDot.classes()).toContain('connected')
    expect(wrapper.text()).toContain('Connected')
  })

  it('displays connection status correctly when disconnected', () => {
    wrapper = createWrapper({ isConnected: false })

    const statusDot = wrapper.find('.status-dot')
    expect(statusDot.classes()).toContain('disconnected')
    expect(wrapper.text()).toContain('Disconnected')
  })

  it('displays axis position information', () => {
    wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('Position: 10.50 mm')
    expect(wrapper.text()).toContain('Position: 5.20 mm')
  })

  it('disables controls when disconnected', () => {
    wrapper = createWrapper({ isConnected: false })

    const distanceInput = wrapper.find('#distance')
    const speedInput = wrapper.find('#speed')
    const homeButtons = wrapper.findAll('.home-btn')
    const directionButtons = wrapper.findAll('.direction-btn')

    expect(distanceInput.element.disabled).toBe(true)
    expect(speedInput.element.disabled).toBe(true)
    homeButtons.forEach(btn => expect(btn.element.disabled).toBe(true))
    directionButtons.forEach(btn => expect(btn.element.disabled).toBe(true))
  })

  it('calls moveAxis API when direction button is clicked', async () => {
    apiService.moveAxis.mockResolvedValue({ data: { success: true } })
    wrapper = createWrapper()

    // Set movement parameters
    await wrapper.find('#distance').setValue('5.0')
    await wrapper.find('#speed').setValue('10.0')

    // Wait for the component to update
    await wrapper.vm.$nextTick()

    // Click right arrow for X axis
    const rightButton = wrapper.findAll('.direction-btn')[1] // Second button should be right arrow
    await rightButton.trigger('click')

    expect(apiService.moveAxis).toHaveBeenCalledWith('x', {
      distance: 5.0,
      speed: 10.0,
      mode: 'relative'
    })
  })

  it('calls moveAxis API with negative distance for left movement', async () => {
    apiService.moveAxis.mockResolvedValue({ data: { success: true } })
    wrapper = createWrapper()

    // Set movement parameters
    await wrapper.find('#distance').setValue('3.0')
    await wrapper.find('#speed').setValue('8.0')

    // Wait for the component to update
    await wrapper.vm.$nextTick()

    // Click left arrow for X axis
    const leftButton = wrapper.findAll('.direction-btn')[0] // First button should be left arrow
    await leftButton.trigger('click')

    expect(apiService.moveAxis).toHaveBeenCalledWith('x', {
      distance: -3.0,
      speed: 8.0,
      mode: 'relative'
    })
  })

  it('calls homeAxis API when home button is clicked', async () => {
    apiService.homeAxis.mockResolvedValue({ data: { success: true } })
    wrapper = createWrapper()
    
    // Click home button for X axis
    const homeButtons = wrapper.findAll('.home-btn')
    await homeButtons[0].trigger('click')
    
    expect(apiService.homeAxis).toHaveBeenCalledWith('x', {
      speed: 10.0
    })
  })

  it('calls setGripperState API when gripper toggle is changed', async () => {
    apiService.setGripperState.mockResolvedValue({ data: { success: true } })
    wrapper = createWrapper()
    
    // Find and toggle the gripper switch
    const gripperToggle = wrapper.find('.toggle-switch input')
    await gripperToggle.setValue(true)
    
    expect(apiService.setGripperState).toHaveBeenCalledWith({
      enabled: true
    })
  })

  it('prevents movement when axis is running', () => {
    const axisData = {
      x: { position: 10.5, running: true },
      z: { position: 5.2, running: false },
      gripper: { enabled: false }
    }
    wrapper = createWrapper({ isConnected: true, axisData })

    const directionButtons = wrapper.findAll('.direction-btn')
    // X axis buttons should be disabled when X axis is running
    expect(directionButtons[0].element.disabled).toBe(true) // Left
    expect(directionButtons[1].element.disabled).toBe(true) // Right
  })

  it('shows error message when API call fails', async () => {
    const errorMessage = 'Connection failed'
    apiService.moveAxis.mockRejectedValue({
      response: { data: { detail: errorMessage } }
    })
    
    wrapper = createWrapper()
    
    // Try to move axis
    const rightButton = wrapper.findAll('.direction-btn')[1]
    await rightButton.trigger('click')
    
    // Wait for next tick to allow error to be set
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.error-message').exists()).toBe(true)
    expect(wrapper.text()).toContain(errorMessage)
  })

  it('updates gripper state from store data', async () => {
    const axisData = {
      x: { position: 10.5, running: false },
      z: { position: 5.2, running: false },
      gripper: { enabled: true }
    }
    wrapper = createWrapper({ isConnected: true, axisData })

    const gripperToggle = wrapper.find('.toggle-switch input')
    expect(gripperToggle.element.checked).toBe(true)
  })

  it('validates movement parameters', () => {
    wrapper = createWrapper()
    
    const distanceInput = wrapper.find('#distance')
    const speedInput = wrapper.find('#speed')
    
    // Check input constraints
    expect(distanceInput.attributes('min')).toBe('0')
    expect(distanceInput.attributes('step')).toBe('0.1')
    expect(speedInput.attributes('min')).toBe('0.1')
    expect(speedInput.attributes('step')).toBe('0.1')
  })

  it('handles missing axis data gracefully', () => {
    wrapper = createWrapper({ isConnected: true, axisData: null })

    // Should not crash and should show N/A for positions
    expect(wrapper.text()).toContain('Position: N/A mm')
  })

  it('shows axis status correctly', () => {
    const axisData = {
      x: { position: 10.5, running: true },
      z: { position: 5.2, running: false },
      gripper: { enabled: false }
    }
    wrapper = createWrapper({ isConnected: true, axisData })

    expect(wrapper.text()).toContain('Status: Moving')
    expect(wrapper.text()).toContain('Status: Stopped')
  })
})
