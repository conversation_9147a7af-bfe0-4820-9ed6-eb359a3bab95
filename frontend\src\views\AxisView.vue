<template>
  <div class="axis-view">
    <h2 class="view-title">Axis Control</h2>

    <!-- Connection Status -->
    <div class="status-card" :class="{ 'error': !connected }">
      <h3>Connection Status</h3>
      <div class="status-indicator">
        <span class="status-dot" :class="connected ? 'connected' : 'disconnected'"></span>
        <span>{{ connected ? 'Connected' : 'Disconnected' }}</span>
      </div>
    </div>

    <!-- Movement Controls -->
    <div class="control-section">
      <h3>Movement Parameters</h3>
      <div class="parameter-controls">
        <div class="input-group">
          <label for="distance">Distance (mm):</label>
          <input
            id="distance"
            v-model.number="movementParams.distance"
            type="number"
            step="0.1"
            min="0"
            :disabled="!connected"
          />
        </div>
        <div class="input-group">
          <label for="speed">Speed (mm/s):</label>
          <input
            id="speed"
            v-model.number="movementParams.speed"
            type="number"
            step="0.1"
            min="0.1"
            :disabled="!connected"
          />
        </div>
      </div>
    </div>

    <!-- Axis Controls -->
    <div class="axis-controls">
      <!-- X Axis Control -->
      <div class="axis-control-card">
        <h3>X Axis</h3>
        <div class="axis-status">
          <div class="status-info">
            <span>Position: {{ axisStatus.x?.position?.toFixed(2) || 'N/A' }} mm</span>
            <span>Status: {{ axisStatus.x?.running ? 'Moving' : 'Stopped' }}</span>
          </div>
          <button
            class="home-btn"
            @click="homeAxis('x')"
            :disabled="!connected || isMoving('x')"
            title="Home X Axis"
          >
            🏠
          </button>
        </div>
        <div class="movement-controls">
          <button
            class="direction-btn left"
            @click="moveAxis('x', -movementParams.distance)"
            :disabled="!connected || isMoving('x')"
            title="Move X Left"
          >
            ←
          </button>
          <button
            class="direction-btn right"
            @click="moveAxis('x', movementParams.distance)"
            :disabled="!connected || isMoving('x')"
            title="Move X Right"
          >
            →
          </button>
        </div>
      </div>

      <!-- Z Axis Control -->
      <div class="axis-control-card">
        <h3>Z Axis</h3>
        <div class="axis-status">
          <div class="status-info">
            <span>Position: {{ axisStatus.z?.position?.toFixed(2) || 'N/A' }} mm</span>
            <span>Status: {{ axisStatus.z?.running ? 'Moving' : 'Stopped' }}</span>
          </div>
          <button
            class="home-btn"
            @click="homeAxis('z')"
            :disabled="!connected || isMoving('z')"
            title="Home Z Axis"
          >
            🏠
          </button>
        </div>
        <div class="movement-controls">
          <button
            class="direction-btn up"
            @click="moveAxis('z', movementParams.distance)"
            :disabled="!connected || isMoving('z')"
            title="Move Z Up"
          >
            ↑
          </button>
          <button
            class="direction-btn down"
            @click="moveAxis('z', -movementParams.distance)"
            :disabled="!connected || isMoving('z')"
            title="Move Z Down"
          >
            ↓
          </button>
        </div>
      </div>
    </div>

    <!-- Gripper Control -->
    <div class="gripper-control">
      <h3>Punch Gripper</h3>
      <div class="gripper-status">
        <span>Status: {{ gripperStatus?.enabled ? 'Activated' : 'Deactivated' }}</span>
        <label class="toggle-switch">
          <input
            type="checkbox"
            v-model="gripperEnabled"
            @change="toggleGripper"
            :disabled="!connected"
          />
          <span class="slider"></span>
        </label>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="error-message">
      <h4>Error</h4>
      <p>{{ error }}</p>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useStatusStore } from '@/stores/status'
import apiService from '@/services/api'

export default {
  name: 'AxisView',
  setup() {
    const statusStore = useStatusStore()

    // Reactive data
    const connected = ref(false)
    const error = ref('')
    const axisStatus = reactive({
      x: null,
      z: null
    })
    const gripperStatus = ref(null)
    const gripperEnabled = ref(false)
    const movementParams = reactive({
      distance: 10.0,
      speed: 5.0
    })
    const movingAxes = reactive({
      x: false,
      z: false
    })

    // Methods
    const updateConnectionStatus = () => {
      connected.value = statusStore.connected && statusStore.backendHealthy
    }

    const updateAxisStatus = () => {
      if (statusStore.axisData) {
        axisStatus.x = statusStore.axisData.x
        axisStatus.z = statusStore.axisData.z
        if (statusStore.axisData.gripper) {
          gripperStatus.value = statusStore.axisData.gripper
          gripperEnabled.value = statusStore.axisData.gripper.enabled || false
        }
      }
    }

    const isMoving = (axis) => {
      return movingAxes[axis] || (axisStatus[axis]?.running || false)
    }

    const moveAxis = async (axis, distance) => {
      if (!connected.value || isMoving(axis)) return

      try {
        error.value = ''
        movingAxes[axis] = true

        const motionData = {
          distance: Math.abs(distance),
          speed: movementParams.speed,
          mode: distance > 0 ? 'relative' : 'relative'
        }

        // For negative distances, we still use relative mode but with negative distance
        if (distance < 0) {
          motionData.distance = -Math.abs(distance)
        }

        await apiService.moveAxis(axis, motionData)
        console.log(`${axis.toUpperCase()} axis movement command sent: ${distance}mm`)

        // Reset moving state after a short delay
        setTimeout(() => {
          movingAxes[axis] = false
        }, 1000)

      } catch (err) {
        console.error(`Error moving ${axis} axis:`, err)
        error.value = `Failed to move ${axis.toUpperCase()} axis: ${err.response?.data?.detail || err.message}`
        movingAxes[axis] = false
      }
    }

    const homeAxis = async (axis) => {
      if (!connected.value || isMoving(axis)) return

      try {
        error.value = ''
        movingAxes[axis] = true

        const homingData = {
          speed: 10.0 // Default homing speed
        }

        await apiService.homeAxis(axis, homingData)
        console.log(`${axis.toUpperCase()} axis homing command sent`)

        // Reset moving state after a short delay
        setTimeout(() => {
          movingAxes[axis] = false
        }, 2000) // Homing takes longer

      } catch (err) {
        console.error(`Error homing ${axis} axis:`, err)
        error.value = `Failed to home ${axis.toUpperCase()} axis: ${err.response?.data?.detail || err.message}`
        movingAxes[axis] = false
      }
    }

    const toggleGripper = async () => {
      if (!connected.value) return

      try {
        error.value = ''

        const gripperData = {
          enabled: gripperEnabled.value
        }

        await apiService.setGripperState(gripperData)
        console.log(`Gripper state set to: ${gripperEnabled.value ? 'enabled' : 'disabled'}`)

      } catch (err) {
        console.error('Error setting gripper state:', err)
        error.value = `Failed to set gripper state: ${err.response?.data?.detail || err.message}`
        // Revert the toggle on error
        gripperEnabled.value = !gripperEnabled.value
      }
    }

    // Lifecycle hooks
    onMounted(() => {
      updateConnectionStatus()
      updateAxisStatus()

      // Watch for status changes
      statusStore.$subscribe(() => {
        updateConnectionStatus()
        updateAxisStatus()
      })
    })

    return {
      connected,
      error,
      axisStatus,
      gripperStatus,
      gripperEnabled,
      movementParams,
      isMoving,
      moveAxis,
      homeAxis,
      toggleGripper
    }
  }
}
</script>

<style scoped>
.axis-view {
  max-width: 1000px;
  padding: 1rem;
}

.view-title {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

/* Status Card */
.status-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.status-card.error {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.status-card h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-dot.connected {
  background-color: #28a745;
}

.status-dot.disconnected {
  background-color: #dc3545;
}

/* Control Section */
.control-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.control-section h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.parameter-controls {
  display: flex;
  gap: 2rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-group label {
  font-weight: 500;
  color: #495057;
}

.input-group input {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  width: 120px;
}

.input-group input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
}

/* Axis Controls */
.axis-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.axis-control-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.axis-control-card h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  text-align: center;
}

.axis-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.9rem;
  color: #495057;
}

.home-btn {
  background-color: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.home-btn:hover:not(:disabled) {
  background-color: #138496;
}

.home-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.movement-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.direction-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 1rem 1.5rem;
  font-size: 1.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 60px;
  min-height: 60px;
}

.direction-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.direction-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Gripper Control */
.gripper-control {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.gripper-control h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.gripper-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #28a745;
}

input:disabled + .slider {
  background-color: #6c757d;
  cursor: not-allowed;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Error Message */
.error-message {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
}

.error-message h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.error-message p {
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .axis-controls {
    grid-template-columns: 1fr;
  }

  .parameter-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .gripper-status {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
</style>
