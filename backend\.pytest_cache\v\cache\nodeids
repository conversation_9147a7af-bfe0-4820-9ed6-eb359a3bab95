["tests/test_axis_api.py::TestAxisAPI::test_cancel_axis_motion_success", "tests/test_axis_api.py::TestAxisAPI::test_get_axis_motion_success", "tests/test_axis_api.py::TestAxisAPI::test_get_axis_status_api_error", "tests/test_axis_api.py::TestAxisAPI::test_get_axis_status_connection_error", "tests/test_axis_api.py::TestAxisAPI::test_get_axis_status_success", "tests/test_axis_api.py::TestAxisAPI::test_get_gripper_state_connection_error", "tests/test_axis_api.py::TestAxisAPI::test_get_gripper_state_success", "tests/test_axis_api.py::TestAxisAPI::test_home_axis_default_speed", "tests/test_axis_api.py::TestAxisAPI::test_home_axis_success", "tests/test_axis_api.py::TestAxisAPI::test_invalid_axis_parameter", "tests/test_axis_api.py::TestAxisAPI::test_invalid_motion_data", "tests/test_axis_api.py::TestAxisAPI::test_move_axis_connection_error", "tests/test_axis_api.py::TestAxisAPI::test_move_axis_success", "tests/test_axis_api.py::TestAxisAPI::test_set_gripper_state_success", "tests/test_recoater_client.py::TestRecoaterClient::test_get_config_success", "tests/test_recoater_client.py::TestRecoaterClient::test_get_drum_success", "tests/test_recoater_client.py::TestRecoaterClient::test_get_drums_success", "tests/test_recoater_client.py::TestRecoaterClient::test_get_state_api_error", "tests/test_recoater_client.py::TestRecoaterClient::test_get_state_connection_error", "tests/test_recoater_client.py::TestRecoaterClient::test_get_state_success", "tests/test_recoater_client.py::TestRecoaterClient::test_get_state_timeout", "tests/test_recoater_client.py::TestRecoaterClient::test_health_check_failure", "tests/test_recoater_client.py::TestRecoaterClient::test_health_check_success", "tests/test_recoater_client.py::TestRecoaterClient::test_non_json_response", "tests/test_recoater_client.py::TestRecoaterClient::test_set_config_success", "tests/test_status_api.py::TestStatusAPI::test_get_status_api_error", "tests/test_status_api.py::TestStatusAPI::test_get_status_connection_error", "tests/test_status_api.py::TestStatusAPI::test_get_status_success", "tests/test_status_api.py::TestStatusAPI::test_get_status_unexpected_error", "tests/test_status_api.py::TestStatusAPI::test_health_check_error", "tests/test_status_api.py::TestStatusAPI::test_health_check_recoater_unhealthy", "tests/test_status_api.py::TestStatusAPI::test_health_check_success", "tests/test_status_api.py::TestStatusAPI::test_root_endpoint"]