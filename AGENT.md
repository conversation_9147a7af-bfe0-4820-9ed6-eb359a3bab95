# **Project Plan: Recoater Custom HMI**

**Version:** 5.0 (Final for Implementation)
**Date:** 9 July 2025

## 1. Project Overview

The primary objective of this project is to develop a modern, intuitive, and reliable web-based Human-Machine Interface (HMI) to control the Aerosint SPD Recoater. The current SwaggerUI, while functional for developers, is not suitable for operators in an industrial or lab environment. This new HMI will replicate and enhance the functionalities presented in Chapter 8 of the Aerosint User Manual, providing a clean and efficient user experience. The system will be built with reliability and ease of use as top priorities, ensuring high uptime and minimal training for non-programmers.

## 2. Core Principles

*   **User-Centric Design:** The UI must be intuitive. All actions should be clearly labeled, and the layout will closely mirror the logical flow presented in the Aerosint manual.
*   **Reliability First:** The application must be robust. This includes graceful error handling (e.g., connection loss), clear status indicators, and maintaining a stable connection to the recoater's API.
*   **Phased and Testable Development:** The project will be broken down into small, logical, and independently testable phases. Each phase will deliver a concrete, working piece of functionality, starting from a simple connection test and building up to the full feature set.
*   **Single Source of Truth:** This document is the definitive guide for the project.

## 3. Technology Stack

*   **Backend:** **FastAPI** (Python) with **Pytest** and **pytest-mock** for testing.
*   **Frontend:** **Vue.js 3** (JavaScript/TypeScript) with **Vite** and **Vitest** for testing.
*   **Communication:** Standard `requests` library, `axios`, and **WebSockets**.
*   **Styling:** **Tailwind CSS** or a component library like **Vuetify**.

## 4. Project Context and Definitions

### 4.1. User Persona: The Researcher in an Industrial Environment

The primary user is a technically proficient researcher operating the machine in a lab or light industrial setting.
*   **Needs:** Data Visibility, Efficient Workflow, Reliability.
*   **UI/UX Implications:** Prioritize a clean layout that avoids clutter but doesn't hide important data. Use clear labels and standard icons. Ensure interactive elements give immediate visual feedback.

### 4.2. Key Assumptions & Clarifications

*   **API Source of Truth:** The `openapi.json` file located in the `/docs` directory is the single source of truth for the recoater hardware's API structure. All backend client methods **must** conform to this specification.
*   **API Limitations:** The hardware API does **not** support on-the-fly changes to `Travel Speed` or `Collectors Delay`. These controls **will be omitted** from the UI.
*   **Configuration Strategy:** The recoater's IP address (`*************`) and Port (`8080`) will be configured by the developer directly in the backend's `.env` file.
*   **Axis Control API:** The `openapi.json` file does not detail endpoints for axis control. We will **assume** they exist and follow a similar pattern to the drum motion endpoints.

### 4.3. Non-Functional Requirements

*   **Polling Rate:** The backend WebSocket will poll the recoater hardware for status updates at a fixed interval of **1 second (1000ms)**.

## 5. Project Execution Strategy

### 5.1. Mocking for Offline Development

All tests **must** be written to run without a connection to the physical recoater hardware. This is achieved through mocking:
*   **Backend (`pytest`):** The `RecoaterClient` service **must be mocked** during testing. Tests will assert that the client's methods were called correctly.
*   **Frontend (`vitest`):** The `axios` library **must be mocked** during testing. Tests will assert that UI interactions trigger the correct backend calls.
*   Keep tests small and simple to simply verify that the functionalities are online and working

### 5.2. Guarantee Against Test Contamination

The project **must** maintain a strict separation between production and test code.
*   **Runtime Patching Only:** All mocking will be performed using `pytest-mock` and `vitest`'s built-in mocking capabilities.
*   **No Test Code in Production:** Test libraries will be listed as development-only dependencies.
*   **No Static Stubs:** The agent is forbidden from creating static stub files that replace production code.

### 5.3. Phase Gating & Workflow

The agent must follow this exact sequence for every phase:
1.  **Implement Code:** Write the feature code for the current phase only.
2.  **Write & Run Tests:** Write the corresponding mocked unit tests according to the strategy above.
3.  **Verification Gate:** Confirm that all new and existing tests pass successfully. **The agent cannot proceed to the next step if any test fails.**
4.  **Log Execution:** The agent must create a detailed log entry in the `EXLOG.md` file for all work done in the phase. This log must adhere to the format specified in section 5.4.
5.  **Commit to Git:** Create a Git commit using the exact message specified for the phase.
6.  **Update Documentation:** Modify the `UG.md` and `DG.md` files to reflect the newly added functionality.
7.  **Check Off Phase:** The agent will then mark the current phase's checkbox as complete (`- [x]`) and move to the next phase.

### 5.4. Execution Log (`EXLOG.md`) Format

All entries in `EXLOG.md` must follow this structure to ensure clarity and auditability.

## Phase X: [Phase Name]

### Action: [A high-level summary of the action, e.g., "Create FastAPI status endpoint"]

**Code Added/Modified:**
# Paste the relevant code snippet here.
# Use appropriate language identifiers for syntax highlighting.


**Rationale:**
(A clear, concise explanation of *why* this action was taken and how it fulfills the requirements of the current phase. For example: "This endpoint was created to satisfy the Phase 1 requirement of proving end-to-end connectivity. It calls the RecoaterClient's get_state method and returns it to the frontend. Error handling is included to ensure the backend remains stable even if the recoater is offline.")

## 6. Detailed Phased Implementation Plan

---
- [x] ## Phase 1: Foundation & Connection Status
    *   **Goal:** Establish the basic project structure and prove end-to-end connectivity.
    *   **Backend Tasks:** Set up FastAPI, `.env` file, `/api/v1/status` endpoint, and a WebSocket for status pushes.
    *   **Frontend Tasks:** Set up Vue.js, basic layout, and a real-time status indicator component.
    *   **Testing Tasks:**
        *   **Backend:** Test the `/api/v1/status` endpoint by mocking the `RecoaterClient`.
        *   **Frontend:** Write a `vitest` smoke test to ensure the status indicator component renders.
    *   **Git Commit:** `feat: phase 1 - implement foundation and real-time connection status`

---
- [ ] ## Phase 2: The "Axis" Window
    *   **Goal:** Implement full, self-contained functionality of the "Axis" window.
    *   **Backend Tasks:** Create `axis.py` API router, add endpoints for axis control, and extend the WebSocket.
    *   **Frontend Tasks:** Create `AxisView.vue` with UI matching Figure 33 and connect it to the WebSocket feed.
    *   **Testing Tasks:**
        *   **Backend:** Test axis motion endpoints by mocking the `RecoaterClient`.
        *   **Frontend:** Test that `AxisView` renders and that motion buttons call the mocked `axios` service.
    *   **Git Commit:** `feat: phase 2 - implement axis control window`

---
- [ ] ## Phase 3: The "Recoater" Window - Drum Controls
    *   **Goal:** Implement the drum control section of the "Recoater" window.
    *   **Backend Tasks:** Create `recoater_controls.py` API router, add drum control endpoints, and extend the WebSocket.
    *   **Frontend Tasks:** Create `RecoaterView.vue` and a reusable `DrumControl.vue` component.
    *   **Testing Tasks:**
        *   **Backend:** Test drum control endpoints by mocking the `RecoaterClient`.
        *   **Frontend:** Test that `DrumControl.vue` correctly displays props and emits events to a mocked parent.
    *   **Git Commit:** `feat: phase 3 - implement drum controls in recoater window`

---
- [ ] ## Phase 4: The "Recoater" Window - Hopper Controls
    *   **Goal:** Implement the hopper (scraping blade) controls.
    *   **Backend Tasks:** Add scraping blade motion endpoints to `recoater_controls.py` and extend the WebSocket.
    *   **Frontend Tasks:** Create and integrate `HopperControl.vue` into `RecoaterView.vue`.
    *   **Testing Tasks:**
        *   **Backend:** Test blade motion endpoints by mocking the `RecoaterClient`.
        *   **Frontend:** Test the `HopperControl.vue` component with mocked services.
    *   **Git Commit:** `feat: phase 4 - implement hopper and scraping blade controls`

---
- [ ] ## Phase 5: The "Recoater" Window - Leveler Control
    *   **Goal:** Implement the leveler control.
    *   **Backend Tasks:** Add leveler pressure endpoint and extend the WebSocket.
    *   **Frontend Tasks:** Create and integrate `LevelerControl.vue`.
    *   **Testing Tasks:**
        *   **Backend:** Test the leveler endpoint by mocking the `RecoaterClient`.
        *   **Frontend:** Smoke test the `LevelerControl.vue` component.
    *   **Git Commit:** `feat: phase 5 - implement leveler control`

---
- [ ] ## Phase 6: The "Print" Window - Parameters & Preview
    *   **Goal:** Implement the non-destructive parts of the print preparation workflow.
    *   **Backend Tasks:** Create `print.py` API router; add endpoints for layer parameters and preview.
    *   **Frontend Tasks:** Create `PrintView.vue`; implement UI for parameters and display the preview image.
    *   **Testing Tasks:**
        *   **Backend:** Test the parameter and preview endpoints by mocking the `RecoaterClient`.
        *   **Frontend:** Test that changing a print parameter calls the mocked `axios` service.
    *   **Git Commit:** `feat: phase 6 - implement print parameters and preview`

---
- [ ] ## Phase 7: The "Print" Window - File & Job Management
    *   **Goal:** Implement the state-changing parts of the print workflow.
    *   **Backend Tasks:** Add endpoints for file upload/deletion and print job start/stop.
    *   **Frontend Tasks:** Add file and job management buttons to `PrintView.vue` with appropriate state handling (e.g., disabling buttons).
    *   **Testing Tasks:**
        *   **Backend:** Test the file upload and job control endpoints by mocking the `RecoaterClient`.
        *   **Frontend:** Test the UI state changes (e.g., button disabling) when a print job is started.
    *   **Git Commit:** `feat: phase 7 - implement print file and job management`

---
- [ ] ## Phase 8: The "Configuration" Window
    *   **Goal:** Implement the final configuration screen.
    *   **Backend Tasks:** Add endpoint to set the main recoater config (`PUT /config`).
    *   **Frontend Tasks:** Create `ConfigurationView.vue`.
    *   **Testing Tasks:**
        *   **Backend:** Test the `PUT /config` endpoint by mocking the `RecoaterClient`.
        *   **Frontend:** Test the `ConfigurationView.vue` component.
    *   **Git Commit:** `feat: phase 8 - implement configuration window`

---
- [ ] ## Phase 9: New Feature - CLI Single Layer Extraction
    *   **Goal:** Allow a user to upload a multi-layer CLI file and print only a single, specified layer.
    *   **Backend Tasks:** Create a new endpoint that accepts a `.cli` file and a `layer_number`, parses the file, creates a temporary single-layer file in memory, and sends it to the recoater.
    *   **Frontend Tasks:** Add a new UI section in `PrintView.vue` with a file upload, a number input for the layer, and a "Load Single Layer" button.
    *   **Testing Tasks:**
        *   **Backend:** Create a sample multi-layer CLI file. Test that the new endpoint calls the mocked `RecoaterClient` with the correctly processed single-layer data.
        *   **Frontend:** Test that the "Load Single Layer" button calls the correct API service function with the file and layer number.
    *   **Git Commit:** `feat: phase 9 - implement CLI single layer extraction feature`

---
- [ ] ## Phase 10: Final Polish & Documentation Review
    *   **Goal:** Perform a final review of the entire application and its documentation.
    *   **Tasks:** Review UI/UX for consistency and clarity. Finalize `README.md`, `UG.md`, and `DG.md`.
    *   **Git Commit:** `docs: phase 10 - final polish and documentation review`

---

## 7. Software Development Best Practices

*   **KISS (Keep It Simple, Stupid):** Do not over-engineer solutions.
*   **DRY (Don't Repeat Yourself):** Extract reusable logic into functions, services, or components.
*   **Separation of Concerns (SoC):**
    *   **Frontend:** UI and user input only.
    *   **Backend:** Bridge/proxy and business logic.
    *   **Recoater Client:** The *only* part of the code that knows how to talk to the hardware API.
*   **Function/Method Rule:** A function should do one thing and generally not exceed 30-40 lines.
*   **File/Module Rule:** A file should represent a single logical entity or feature.
*   **Strict Typing:** Use Python's Type Hints for all backend code.
*   **Data Validation:** Use Pydantic models for all API request/response bodies.
*   **Component-Based Architecture (Vue):** Break the UI into the smallest logical pieces.
*   **Data Flow (Vue):** Props down, events up. No direct modification of props in a child.
*   **API Service Layer (Vue):** All `axios` calls must be in a dedicated service file.
*   **Version Control:** Use Conventional Commits (`feat:`, `fix:`, `docs:`, etc.).

---

### **Final Instruction for the Implementing AI Agent**

You are tasked with building the Recoater Custom HMI.
1.  **Follow the `Detailed Phased Implementation Plan` (v5.0) precisely.** Do not skip or combine phases.
2.  **Adhere strictly to the `Project Execution Strategy`**, especially the **`Mocking for Offline Development`** and **`Guarantee Against Test Contamination`** sections.
3.  **Follow the `Phase Gating & Workflow` for each phase:**
    *   Implement the code.
    *   Write and run the mocked tests.
    *   **Proceed ONLY if all tests pass.**
    *   **Create a detailed execution log in `EXLOG.md`** for the phase, following the specified format.
    *   Create the Git commit with the specified message.
    *   Update the `UG.md` and `DG.md` documentation files.
    *   Mark the phase's checkbox as complete.