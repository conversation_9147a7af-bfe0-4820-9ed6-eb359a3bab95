"""
Axis API Router
===============

This module provides the axis control API endpoints for the Recoater HMI.
It handles requests for axis movement, homing, and gripper control.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Literal
import logging

from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/axis", tags=["axis"])

def get_recoater_client() -> RecoaterClient:
    """Dependency to get the recoater client instance."""
    from app.main import recoater_client
    if recoater_client is None:
        raise HTTPException(status_code=503, detail="Recoater client not initialized")
    return recoater_client

# Pydantic models for request/response validation

class AxisMotionRequest(BaseModel):
    """Request model for axis motion commands."""
    distance: float = Field(..., description="Distance to move in mm")
    speed: float = Field(..., gt=0, description="Movement speed in mm/s")
    mode: Literal["relative", "absolute"] = Field(default="relative", description="Movement mode")

class AxisHomingRequest(BaseModel):
    """Request model for axis homing commands."""
    speed: float = Field(default=10.0, gt=0, description="Homing speed in mm/s")

class GripperStateRequest(BaseModel):
    """Request model for gripper state changes."""
    enabled: bool = Field(..., description="True to activate gripper, False to deactivate")

# Axis Status Endpoints

@router.get("/{axis}")
async def get_axis_status(
    axis: Literal["x", "z", "gripper"],
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current status of a specific axis.
    
    Args:
        axis: The axis identifier ('x', 'z', or 'gripper')
        
    Returns:
        Dictionary containing axis status information
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting status for axis: {axis}")
        status_data = client.get_axis_status(axis)
        
        response = {
            "axis": axis,
            "status": status_data,
            "connected": True
        }
        
        logger.debug(f"Axis {axis} status retrieved successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while getting axis {axis} status: {e}")
        return {
            "axis": axis,
            "status": None,
            "connected": False,
            "error": "Failed to connect to recoater hardware",
            "error_details": str(e)
        }
        
    except RecoaterAPIError as e:
        logger.error(f"API error while getting axis {axis} status: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Recoater API error: {str(e)}"
        )
        
    except Exception as e:
        logger.error(f"Unexpected error while getting axis {axis} status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

# Axis Motion Endpoints

@router.post("/{axis}/motion")
async def move_axis(
    axis: Literal["x", "z"],
    motion_request: AxisMotionRequest,
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Move an axis by the specified distance.
    
    Args:
        axis: The axis identifier ('x' or 'z')
        motion_request: Motion parameters (distance, speed, mode)
        
    Returns:
        Dictionary containing motion command response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Moving axis {axis}: {motion_request.dict()}")
        response_data = client.move_axis(
            axis=axis,
            distance=motion_request.distance,
            speed=motion_request.speed,
            mode=motion_request.mode
        )
        
        response = {
            "axis": axis,
            "command": "motion",
            "parameters": motion_request.dict(),
            "response": response_data,
            "success": True
        }
        
        logger.debug(f"Axis {axis} motion command sent successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while moving axis {axis}: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to recoater hardware: {str(e)}"
        )
        
    except RecoaterAPIError as e:
        logger.error(f"API error while moving axis {axis}: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Recoater API error: {str(e)}"
        )
        
    except Exception as e:
        logger.error(f"Unexpected error while moving axis {axis}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/{axis}/home")
async def home_axis(
    axis: Literal["x", "z"],
    homing_request: AxisHomingRequest,
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Home a specific axis.
    
    Args:
        axis: The axis identifier ('x' or 'z')
        homing_request: Homing parameters (speed)
        
    Returns:
        Dictionary containing homing command response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Homing axis {axis} at speed {homing_request.speed}")
        response_data = client.home_axis(
            axis=axis,
            speed=homing_request.speed
        )
        
        response = {
            "axis": axis,
            "command": "homing",
            "parameters": homing_request.dict(),
            "response": response_data,
            "success": True
        }
        
        logger.debug(f"Axis {axis} homing command sent successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while homing axis {axis}: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to recoater hardware: {str(e)}"
        )
        
    except RecoaterAPIError as e:
        logger.error(f"API error while homing axis {axis}: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Recoater API error: {str(e)}"
        )
        
    except Exception as e:
        logger.error(f"Unexpected error while homing axis {axis}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/{axis}/motion")
async def get_axis_motion(
    axis: Literal["x", "z"],
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current motion command for an axis.
    
    Args:
        axis: The axis identifier ('x' or 'z')
        
    Returns:
        Dictionary containing current motion information
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting motion status for axis: {axis}")
        motion_data = client.get_axis_motion(axis)
        
        response = {
            "axis": axis,
            "motion": motion_data,
            "connected": True
        }
        
        logger.debug(f"Axis {axis} motion status retrieved successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while getting axis {axis} motion: {e}")
        return {
            "axis": axis,
            "motion": None,
            "connected": False,
            "error": "Failed to connect to recoater hardware",
            "error_details": str(e)
        }
        
    except RecoaterAPIError as e:
        logger.error(f"API error while getting axis {axis} motion: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Recoater API error: {str(e)}"
        )
        
    except Exception as e:
        logger.error(f"Unexpected error while getting axis {axis} motion: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.delete("/{axis}/motion")
async def cancel_axis_motion(
    axis: Literal["x", "z"],
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Cancel the current motion command for an axis.
    
    Args:
        axis: The axis identifier ('x' or 'z')
        
    Returns:
        Dictionary containing cancellation response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Cancelling motion for axis: {axis}")
        response_data = client.cancel_axis_motion(axis)
        
        response = {
            "axis": axis,
            "command": "cancel_motion",
            "response": response_data,
            "success": True
        }
        
        logger.debug(f"Axis {axis} motion cancelled successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while cancelling axis {axis} motion: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to recoater hardware: {str(e)}"
        )
        
    except RecoaterAPIError as e:
        logger.error(f"API error while cancelling axis {axis} motion: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Recoater API error: {str(e)}"
        )
        
    except Exception as e:
        logger.error(f"Unexpected error while cancelling axis {axis} motion: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

# Gripper Control Endpoints

@router.put("/gripper/state")
async def set_gripper_state(
    gripper_request: GripperStateRequest,
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the gripper state (punch gripper).

    Args:
        gripper_request: Gripper state parameters (enabled)

    Returns:
        Dictionary containing gripper state response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting gripper state: {gripper_request.enabled}")
        response_data = client.set_gripper_state(gripper_request.enabled)

        response = {
            "command": "set_gripper_state",
            "parameters": gripper_request.dict(),
            "response": response_data,
            "success": True
        }

        logger.debug(f"Gripper state set successfully: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error while setting gripper state: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to recoater hardware: {str(e)}"
        )

    except RecoaterAPIError as e:
        logger.error(f"API error while setting gripper state: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Recoater API error: {str(e)}"
        )

    except Exception as e:
        logger.error(f"Unexpected error while setting gripper state: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/gripper/state")
async def get_gripper_state(
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current gripper state.

    Returns:
        Dictionary containing gripper state information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info("Getting gripper state")
        state_data = client.get_gripper_state()

        response = {
            "gripper_state": state_data,
            "connected": True
        }

        logger.debug(f"Gripper state retrieved successfully: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error while getting gripper state: {e}")
        return {
            "gripper_state": None,
            "connected": False,
            "error": "Failed to connect to recoater hardware",
            "error_details": str(e)
        }

    except RecoaterAPIError as e:
        logger.error(f"API error while getting gripper state: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Recoater API error: {str(e)}"
        )

    except Exception as e:
        logger.error(f"Unexpected error while getting gripper state: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
